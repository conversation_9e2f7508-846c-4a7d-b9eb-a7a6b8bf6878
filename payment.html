<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscribe to Turbo Plan - Sentio</title>
    <meta name="description" content="Subscribe to Sentio's Turbo Plan and unlock powerful e-commerce features for your business.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-blue': '#3674B5',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for payment form */
        .payment-container {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            min-height: 100vh;
        }

        .payment-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .plan-summary {
            background: #1a1a1a;
            color: white;
        }

        .form-input {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3674B5;
            box-shadow: 0 0 0 3px rgba(54, 116, 181, 0.1);
        }

        .payment-method-option {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .payment-method-option.selected {
            border-color: #3674B5;
            background-color: rgba(54, 116, 181, 0.05);
        }

        .subscribe-button {
            background: #3674B5;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 16px 24px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .subscribe-button:hover {
            background: #2d5a94;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(54, 116, 181, 0.3);
        }

        .card-icons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .card-icon {
            width: 32px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .visa { background: #1a1f71; }
        .mastercard { background: #eb001b; }
        .amex { background: #006fcf; }
        .discover { background: #ff6000; }

        /* Business information section */
        .business-info {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out, padding 0.3s ease-out;
            opacity: 0;
            padding: 0;
        }

        .business-info.show {
            max-height: 300px;
            opacity: 1;
            padding: 16px 0 0 0;
        }

        .info-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            background: #6b7280;
            color: white;
            border-radius: 50%;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
            cursor: help;
        }
    </style>
</head>

<body class="font-inter">
    <div class="payment-container">
        <div class="container mx-auto px-4 py-8">
            <div class="max-w-6xl mx-auto">
                <!-- Header -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold text-white font-dm-sans tracking-wide mb-2">
                        Sentio
                    </h1>
                    <p class="text-gray-300">Complete your subscription</p>
                </div>

                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Left Column - Plan Summary -->
                    <div class="plan-summary rounded-2xl p-8">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-white rounded flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold">Subscribe to Turbo Plan</h2>
                        </div>

                        <div class="mb-8">
                            <div class="flex items-baseline mb-2">
                                <span class="text-4xl font-bold">$50.00</span>
                                <span class="text-gray-400 ml-2">per month</span>
                            </div>
                        </div>

                        <div class="space-y-4 mb-8">
                            <h3 class="font-semibold text-lg">Turbo Plan</h3>
                            <p class="text-gray-300 text-sm">10 GB of storage per month, Unlimited API requests, Priority support</p>
                            <p class="text-gray-400 text-sm">Billed monthly</p>
                        </div>

                        <div class="border-t border-gray-600 pt-6">
                            <div class="flex justify-between items-center mb-2">
                                <span>Subtotal</span>
                                <span>$50.00</span>
                            </div>
                            <div class="mb-4">
                                <button class="text-brand-blue text-sm hover:underline">Add promotion code</button>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span>Tax</span>
                                <span>$0.00</span>
                            </div>
                            <div class="border-t border-gray-600 pt-4 mt-4">
                                <div class="flex justify-between items-center text-lg font-semibold">
                                    <span>Total due today</span>
                                    <span>$50.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Payment Form -->
                    <div class="payment-card rounded-2xl p-8">
                        <form id="paymentForm">
                            <!-- Contact Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold mb-4">Contact information</h3>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                    <input type="email" class="form-input w-full" placeholder="<EMAIL>" required>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold mb-4">Payment method</h3>

                                <!-- Card Option -->
                                <div class="payment-method-option selected mb-4" data-method="card">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <input type="radio" name="paymentMethod" value="card" checked class="mr-3">
                                            <span class="font-medium">Card</span>
                                        </div>
                                        <div class="card-icons">
                                            <div class="card-icon visa">VISA</div>
                                            <div class="card-icon mastercard">MC</div>
                                            <div class="card-icon amex">AMEX</div>
                                            <div class="card-icon discover">DISC</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bank Option -->
                                <div class="payment-method-option mb-6" data-method="bank">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <input type="radio" name="paymentMethod" value="bank" class="mr-3">
                                            <span class="font-medium">Bank</span>
                                        </div>
                                        <div class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                            10% back
                                        </div>
                                    </div>
                                </div>

                                <!-- Card Information -->
                                <div id="cardInfo" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Card information</label>
                                        <input type="text" class="form-input w-full" placeholder="1234 1234 1234 1234" maxlength="19" required>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <input type="text" class="form-input" placeholder="MM / YY" maxlength="5" required>
                                        <input type="text" class="form-input" placeholder="CVC" maxlength="4" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Cardholder name</label>
                                        <input type="text" class="form-input w-full" placeholder="Full name on card" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Country or region</label>
                                        <select class="form-input w-full" required>
                                            <option value="">Select country</option>
                                            <option value="PH">Philippines</option>
                                            <option value="US">United States</option>
                                            <option value="CA">Canada</option>
                                            <option value="GB">United Kingdom</option>
                                            <option value="AU">Australia</option>
                                        </select>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" id="businessPurchase" class="mr-2">
                                        <label for="businessPurchase" class="text-sm text-gray-600">I'm purchasing as a business</label>
                                    </div>

                                    <!-- Business Information Section -->
                                    <div id="businessInfo" class="business-info">
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                                    TIN information
                                                    <span class="info-icon" title="Tax Identification Number for business purchases">i</span>
                                                </label>
                                                <input type="text" class="form-input w-full mb-3" placeholder="Business name" id="businessName">
                                                <div class="flex gap-3">
                                                    <select class="form-input flex-1" id="tinCountry">
                                                        <option value="PH_TIN">🇵🇭 PH_TIN</option>
                                                        <option value="US_EIN">🇺🇸 US_EIN</option>
                                                        <option value="CA_BN">🇨🇦 CA_BN</option>
                                                        <option value="GB_VAT">🇬🇧 GB_VAT</option>
                                                        <option value="AU_ABN">🇦🇺 AU_ABN</option>
                                                    </select>
                                                    <input type="text" class="form-input flex-1" placeholder="123456789012" id="tinNumber" maxlength="12">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Information -->
                            <div class="mb-8">
                                <div class="flex items-center">
                                    <input type="checkbox" id="saveInfo" class="mr-2">
                                    <label for="saveInfo" class="text-sm text-gray-600">Save my information for faster checkout</label>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Pay faster on Wix and everywhere Link is accepted.</p>
                            </div>

                            <!-- Subscribe Button -->
                            <button type="submit" class="subscribe-button mb-6">
                                Subscribe
                            </button>

                            <!-- Terms -->
                            <div class="text-center text-xs text-gray-500 space-y-2">
                                <p>By subscribing, you authorize Wix to charge your account to the Terms and Conditions.</p>
                                <p>By placing your order, you agree to our <a href="#" class="text-brand-blue hover:underline">Terms of Service</a> and <a href="#" class="text-brand-blue hover:underline">Privacy Policy</a>.</p>
                                <div class="flex items-center justify-center space-x-4 mt-4">
                                    <span>Powered by</span>
                                    <span class="font-semibold">stripe</span>
                                    <span>•</span>
                                    <a href="#" class="text-brand-blue hover:underline">Legal</a>
                                    <span>•</span>
                                    <a href="#" class="text-brand-blue hover:underline">Contact</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Payment method selection
        document.querySelectorAll('.payment-method-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                document.querySelectorAll('.payment-method-option').forEach(opt => {
                    opt.classList.remove('selected');
                });

                // Add selected class to clicked option
                this.classList.add('selected');

                // Update radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // Show/hide card information based on selection
                const cardInfo = document.getElementById('cardInfo');
                if (radio.value === 'card') {
                    cardInfo.style.display = 'block';
                } else {
                    cardInfo.style.display = 'none';
                }
            });
        });

        // Card number formatting
        document.querySelector('input[placeholder="1234 1234 1234 1234"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // Expiry date formatting
        document.querySelector('input[placeholder="MM / YY"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + ' / ' + value.substring(2, 4);
            }
            e.target.value = value;
        });

        // CVC input validation
        document.querySelector('input[placeholder="CVC"]').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // Business purchase checkbox functionality
        document.getElementById('businessPurchase').addEventListener('change', function() {
            const businessInfo = document.getElementById('businessInfo');
            const businessName = document.getElementById('businessName');
            const tinNumber = document.getElementById('tinNumber');

            if (this.checked) {
                businessInfo.classList.add('show');
                // Make business fields required when checkbox is checked
                businessName.setAttribute('required', 'required');
                tinNumber.setAttribute('required', 'required');
            } else {
                businessInfo.classList.remove('show');
                // Remove required attribute when checkbox is unchecked
                businessName.removeAttribute('required');
                tinNumber.removeAttribute('required');
                // Clear the values
                businessName.value = '';
                tinNumber.value = '';
            }
        });

        // TIN number formatting and validation
        document.getElementById('tinNumber').addEventListener('input', function(e) {
            // Remove any non-numeric characters
            let value = e.target.value.replace(/[^0-9]/g, '');
            // Limit to 12 digits
            if (value.length > 12) {
                value = value.substring(0, 12);
            }
            e.target.value = value;
        });

        // Form submission
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate business information if checkbox is checked
            const businessPurchase = document.getElementById('businessPurchase');
            if (businessPurchase.checked) {
                const businessName = document.getElementById('businessName').value.trim();
                const tinNumber = document.getElementById('tinNumber').value.trim();

                if (!businessName) {
                    alert('Please enter your business name.');
                    document.getElementById('businessName').focus();
                    return;
                }

                if (!tinNumber || tinNumber.length < 9) {
                    alert('Please enter a valid TIN number (at least 9 digits).');
                    document.getElementById('tinNumber').focus();
                    return;
                }
            }

            // Show loading state
            const submitBtn = document.querySelector('.subscribe-button');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Processing...';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.7';

            // Simulate payment processing
            setTimeout(() => {
                const businessText = businessPurchase.checked ? ' Your business information has been recorded.' : '';
                alert('Payment successful! Welcome to the Turbo Plan!' + businessText);
                // In a real application, you would redirect to a success page
                // window.location.href = 'success.html';

                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                submitBtn.style.opacity = '1';
            }, 2000);
        });

        // Add promotion code functionality
        document.addEventListener('DOMContentLoaded', function() {
            const promoButton = document.querySelector('.text-brand-blue');
            if (promoButton) {
                promoButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const promoCode = prompt('Enter your promotion code:');
                    if (promoCode) {
                        // In a real application, you would validate the promo code
                        alert('Promotion code applied successfully!');
                    }
                });
            }
        });
    </script>
</body>
</html>